<?php

use App\NotificationJobs\Evaluation\ViewNotificationJob;
use App\Services\ReferenceIdentifierService;

include_once(__DIR__ . '/class_FinalizeEvaluation.php');

	class Bid {
		
		private $db;
		private $evaluationID;
		private $userID;
		private $companyID;
		private $bidAcceptanceName;
		private $bidAcceptanceAmount;
		private $bidAcceptanceNumber;
		private $projectCompleteName;
		private $projectCompleteAmount;
		private $projectCompleteNumber;
		private $bidAcceptanceSplit;
		private $projectCompleteSplit;
		private $bidSubTotal;
		private $bidTotal;
		private $customEvaluation;
		private $bidItemID;
		private $bidID;
		private $bidNumber;
		private $results;
		
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		//FXLRATR-177 added parameters
		public function setEvaluation($evaluationID, $bidNumber, $userID, $companyID, $bidAcceptanceName, $bidAcceptanceAmount, $bidAcceptanceNumber, $projectCompleteName, $projectCompleteAmount, $projectCompleteNumber, $bidAcceptanceSplit, $projectCompleteSplit, $bidSubTotal, $bidTotal, $customEvaluation, $bidItemID = null) {
		    $id_service = new ReferenceIdentifierService((int) $companyID, ReferenceIdentifierService::TYPE_BID);

			$this->evaluationID = $evaluationID;
			$this->bidNumber = $id_service->getNext();
			$this->userID = $userID;
			$this->companyID = $companyID;
			$this->bidAcceptanceName = $bidAcceptanceName; 
			$this->bidAcceptanceAmount = $bidAcceptanceAmount;
			$this->bidAcceptanceNumber = $bidAcceptanceNumber;
			$this->projectCompleteName =$projectCompleteName; 
			$this->projectCompleteAmount = $projectCompleteAmount;
			$this->projectCompleteNumber =$projectCompleteNumber; 
			$this->bidAcceptanceSplit = $bidAcceptanceSplit;
			$this->projectCompleteSplit = $projectCompleteSplit;
			$this->bidSubTotal = $bidSubTotal;
			$this->bidTotal = $bidTotal;
			$this->customEvaluation = $customEvaluation;
			$this->bidItemID = $bidItemID;

			//FXLRATR-177
			
			$charid = strtoupper(md5(uniqid(rand(), true)));
			$hyphen = chr(45);// "-"
			$this->bidID = 
				substr($charid, 0, 8).$hyphen
				.substr($charid, 8, 4).$hyphen
				.substr($charid,12, 4).$hyphen
				.substr($charid,16, 4).$hyphen
				.substr($charid,20,12);
			
		}
			
			
			
		public function addBid() {
			if (!empty($this->evaluationID)) {
				
				$getContractID = $this->db->prepare("SELECT contractID
				
				FROM `companyContract`
				
				WHERE  companyID =  :companyID ORDER BY contractID DESC LIMIT 1");
				
				$getContractID->bindParam(':companyID', $this->companyID);	 
				
				$getContractID->execute();
				
				if ($getContractID->rowCount()>=1) {
					while ($row = $getContractID->fetch((PDO::FETCH_ASSOC))) {
					$contractID = $row['contractID'] ;
					}
					
				} 
				
				if ($this->customEvaluation == 'true') {
                    $st = $this->db->prepare("UPDATE `customBid` SET 
                    
                    `bidAcceptanceName` = :bidAcceptanceName, 
                    `bidAcceptanceAmount` = :bidAcceptanceAmount, 
                    `bidAcceptanceNumber` = :bidAcceptanceNumber, 
                    `projectCompleteName` = :projectCompleteName, 
                    `projectCompleteAmount` = :projectCompleteAmount, 
                    `projectCompleteNumber` = :projectCompleteNumber, 
                    `bidAcceptanceSplit` = :bidAcceptanceSplit, 
                    `projectCompleteSplit` = :projectCompleteSplit, 
                    `bidTotal` = :bidTotal,
                    `isBidCreated` = '1',
                    `bidID` = :bidID,
                    `bidFirstSent` = UTC_TIMESTAMP,
                    `bidFirstSentByID` = :bidFirstSentByID,
                    `contractID` = :contractID,
                    `bidNumber` = :bidNumber
                    
                    WHERE evaluationID = :evaluationID
                    ");

					$st->bindParam(':bidAcceptanceName', $this->bidAcceptanceName); 
					$st->bindParam(':bidAcceptanceAmount', $this->bidAcceptanceAmount);	
					$st->bindParam(':bidAcceptanceNumber', $this->bidAcceptanceNumber);	
					$st->bindParam(':projectCompleteName', $this->projectCompleteName); 
					$st->bindParam(':projectCompleteAmount', $this->projectCompleteAmount);
					$st->bindParam(':projectCompleteNumber', $this->projectCompleteNumber);
					$st->bindParam(':bidAcceptanceSplit', $this->bidAcceptanceSplit);
					$st->bindParam(':projectCompleteSplit', $this->projectCompleteSplit);
					$st->bindParam(':bidTotal', $this->bidTotal);
					$st->bindParam(':bidID', $this->bidID);
					$st->bindParam(':evaluationID', $this->evaluationID);
					$st->bindParam(':bidFirstSentByID', $this->userID);
					$st->bindParam(':contractID', $contractID);
					$st->bindParam(':bidNumber', $this->bidNumber);
					
					$st->execute();

                    $finalize_evaluation = new FinalizeEvaluation();
                    $finalize_evaluation->setEvaluation($this->evaluationID, $this->companyID, $this->userID);
                    $finalize_evaluation->sendEvaluation();
				} else {
					$st = $this->db->prepare("
						UPDATE `evaluationBid`
						
						SET	
						
						`bidAcceptanceName` = :bidAcceptanceName,     
						`bidAcceptanceAmount` = :bidAcceptanceAmount,
						`bidAcceptanceNumber` = :bidAcceptanceNumber,
						`projectCompleteName` = :projectCompleteName, 
						`projectCompleteAmount` = :projectCompleteAmount,
						`projectCompleteNumber` = :projectCompleteNumber,
						`bidAcceptanceSplit` = :bidAcceptanceSplit,
						`projectCompleteSplit` = :projectCompleteSplit,
						`bidSubTotal` = :bidSubTotal,
						`bidTotal` = :bidTotal,
						`isBidCreated` = '1',
						`bidID` = :bidID,
						`bidFirstSent` = UTC_TIMESTAMP,
						`bidFirstSentByID` = :bidFirstSentByID,
						`contractID` = :contractID,
						`bidNumber` = :bidNumber
						
						WHERE evaluationID = :evaluationID");

					$st->bindParam(':bidAcceptanceName', $this->bidAcceptanceName); 
					$st->bindParam(':bidAcceptanceAmount', $this->bidAcceptanceAmount);
					$st->bindParam(':bidAcceptanceNumber', $this->bidAcceptanceNumber);
					$st->bindParam(':projectCompleteName', $this->projectCompleteName); 
					$st->bindParam(':projectCompleteAmount', $this->projectCompleteAmount);
					$st->bindParam(':projectCompleteNumber', $this->projectCompleteNumber);
					$st->bindParam(':bidAcceptanceSplit', $this->bidAcceptanceSplit);
					$st->bindParam(':projectCompleteSplit', $this->projectCompleteSplit);
					$st->bindParam(':bidSubTotal', $this->bidSubTotal);
					$st->bindParam(':bidTotal', $this->bidTotal);
					$st->bindParam(':bidID', $this->bidID);
					$st->bindParam(':evaluationID', $this->evaluationID);
					$st->bindParam(':bidFirstSentByID', $this->userID);
					$st->bindParam(':contractID', $contractID);
					$st->bindParam(':bidNumber', $this->bidNumber);
					$st->execute();
				}

				ViewNotificationJob::enqueue((int) $this->evaluationID);

                $this->results = 'true';
            }
		}
		
		public function getResults () {
			// var_dump($this->results);
			// exit();
		 	return $this->results;
		}
		
	}
	

?>