'use strict';

const $ = require('jquery');
const EventEmitter = require('events');
const lang = require('lodash/lang');
const includes = require('lodash/includes');
const {Reveal} = require('foundation-sites/js/foundation.reveal');

/**
 * @type {module:FlashMessage}
 */
const FlashMessage = require('@ca-submodule/flash-message');

const base_tpl = require('@cas-modal-tpl/base.hbs');
const action_tpl = require('@cas-modal-tpl/action.hbs');

const Sizes = {
    TINY: 1,
    SMALL: 2,
    LARGE: 3,
    FULL: 4
};

const FoundationSizes = {
    1: 'tiny',
    2: 'small',
    3: 'large',
    4: 'full'
};

/**
 * @memberof module:Modal
 */
class Base {
    /**
     * Constructor
     *
     * @param {string} content - Content for modal
     * @param {Object} config - Modal config
     * @param {string} config.size - Size of modal
     * @param {?boolean} [config.closable=false] - Determines if a close button should be added and if esc key press should close modal
     * @param {?boolean} [config.wrapper=true] - Determines if the wrapper template for modals should be added
     * @param {Array} [config.classes=[]] - Array of classes to add to modal wrapper
     */
    constructor(content, config) {
        if (!includes(Sizes, config.size)) {
            throw new Error('Invalid size');
        }
        if (!lang.isBoolean(config.closable)) {
            config.closable = false;
        }
        if (!lang.isBoolean(config.wrapper)) {
            config.wrapper = true;
        }
        if (!lang.isArray(config.classes)) {
            config.classes = [];
        }
        this.elem = {
            root: $(base_tpl({
                size: FoundationSizes[config.size],
                classes: config.classes.length > 0 ? ` ${config.classes.join(' ')}` : '',
                wrapper: config.wrapper,
                closable: config.closable
            }))
        };
        $('body').append(this.elem.root.hide());
        this.elem.content = config.wrapper ? this.elem.root.fxFind('modal-content') : this.elem.root;
        if (this.elem.content.length === 0) {
            throw new Error('Unable to find content');
        }
        /**
         * @protected
         */
        this.state = {
            reveal: new Reveal(this.elem.root, {closeOnClick: config.closable, closeOnEsc: config.closable}),
            working: false,
            actions: new Map,
            action_idx: 0,
            messages: null,
            events: new EventEmitter,
            type_configs: {
                [Base.Action.SAVE]: {
                    label: 'Save',
                    classes: ['t-primary'],
                    handler: () => this.fire('save')
                },
                [Base.Action.YES]: {
                    label: 'Yes',
                    classes: ['t-primary'],
                    handler: () => this.fire('yes')
                },
                [Base.Action.CANCEL]: {
                    label: 'Cancel',
                    classes: ['t-cancel'],
                    handler: () => this.fire('cancel')
                },
                [Base.Action.NO]: {
                    label: 'No',
                    classes: ['t-cancel'],
                    handler: () => this.fire('no')
                },
                [Base.Action.DELETE]: {
                    label: 'Delete',
                    classes: ['t-delete'],
                    handler: () => this.fire('delete')
                },
                [Base.Action.CLOSE]: {
                    label: 'Close',
                    classes: ['t-cancel'],
                    handler: () => this.close()
                },
                [Base.Action.DELETE_PRIMARY]: {
                    label: 'Delete',
                    classes: ['t-delete-primary'],
                    handler: () => this.fire('delete')
                }
            }
        };
        this.elem.root.css('max-height', ($(window).height()) * .8);
        this.elem.root.on('closed.zf.reveal', (e) => {
            this.fire('close', e);
        });
        if (!lang.isUndefined(content)) {
            this.setContent(content);
        }

        this.elem.title = this.elem.root.fxFind('modal-title');
        if (this.elem.title.length === 0) {
            this.elem.title = null;
        }
        this.elem.working = this.elem.root.fxFind('modal-working');
        if (this.elem.working.length === 0) {
            this.elem.working = null;
        }
        this.elem.actions = this.elem.root.fxFind('modal-actions');
        if (this.elem.actions.length === 0) {
            this.elem.actions = null;
        }
        if (config.closable) {
            this.elem.close = this.elem.root.find('[data-close]').fxClick((e) => {
                e.preventDefault();
                this.close();
                return false;
            });
        }

        if (this.elem.actions !== null) {
            const that = this;
            this.elem.actions.fxClickWatcher('action', function (e) {
                e.preventDefault();
                that.handleAction(parseInt($(this).data('idx')));
                return false;
            });
        }
    };

    /**
     * Sizes
     *
     * @readonly
     *
     * @returns {{TINY: number, SMALL: number, LARGE: number, FULL: number}}
     */
    static get Size() {
        return Sizes;
    };

    /**
     * Actions
     *
     * @readonly
     *
     * @returns {{SAVE: number, CANCEL: number, DELETE: number, YES: number, NO: number, CLOSE: number, DELETE_PRIMARY: number}}
     */
    static get Action() {
        return {
            SAVE: 1,
            CANCEL: 2,
            DELETE: 3,
            YES: 4,
            NO: 5,
            CLOSE: 6,
            DELETE_PRIMARY: 7
        };
    };

    /**
     * Add event listener
     *
     * @param {string} event
     * @param {function} closure
     * @returns {module:Modal.Base}
     */
    on(event, closure) {
        this.state.events.on(event, closure);
        return this;
    };

    /**
     * Fire event
     *
     * Alias for calling emit
     *
     * @protected
     *
     * @param {string} event
     * @param {Object} [data={}]
     */
    fire(event, data = {}) {
        this.state.events.emit(event, data);
    };

    /**
     * Get messages instance
     *
     * Will create one if it doesn't exist already
     *
     * @returns {module:FlashMessage.Controller}
     */
    get messages() {
        if (this.state.messages === null) {
            let messages = new FlashMessage.Controller();
            this.elem.messages = this.elem.root.fxFind('modal-messages');
            this.elem.messages.append(messages.render());
            messages.boot(this.elem.messages);
            this.state.messages = messages;
        }
        return this.state.messages;
    };

    /**
     * Show info flash message
     *
     * @param {string} text
     * @returns {module:FlashMessage.Message}
     */
    showInfoMessage(text) {
        let message = FlashMessage.Message.info(text);
        this.messages.addMessage(message);
        return message;
    };

    /**
     * Show error flash message
     *
     * @param {string} text
     * @returns {module:FlashMessage.Message}
     */
    showErrorMessage(text) {
        let message = FlashMessage.Message.error(text);
        this.messages.addMessage(message);
        return message;
    };

    /**
     * Clear all messages
     */
    clearMessages() {
        if (this.state.messages === null) {
            return;
        }
        this.state.messages.deleteAllMessages();
    };

    /**
     * Get modal content
     *
     * @returns {jQuery} - jQuery element containing content
     */
    getContent() {
        return this.elem.content;
    };

    /**
     * Set modal title
     *
     * @param {string} title
     */
    setTitle(title) {
        if (this.elem.title === null) {
            throw new Error('No title container found in modal');
        }
        this.elem.title.html(title);
    };

    /**
     * Set internal content for modal
     *
     * @param {string} content
     * @returns {module:Modal.Base}
     */
    setContent(content) {
        if (content instanceof $) {
            this.elem.content.empty().append(content);
            return this;
        }
        this.elem.content.html(content);
        return this;
    };

    /**
     * Handle action click
     *
     * @param {number} idx
     */
    handleAction(idx) {
        let action = this.state.actions.get(idx);
        action.handler();
    };

    /**
     * Add action
     *
     * @param {Object} action - Action config
     * @param {?number} action.type - Action type (used to make preconfigured actions)
     * @param {?string} action.label - Action label
     * @param {?Array} action.classes - Extra button classes
     * @param {Object} [config={}] - Action config
     * @param {?number} config.position_before - Index of action to position this action before
     * @param {?number} config.position_after - Index of action to position this action after
     */
    addAction(action, config = {}) {
        if (this.elem.actions === null) {
            throw new Error('No actions container found in modal');
        }
        action.idx = ++this.state.action_idx;
        if (!lang.isArray(action.classes)) {
            action.classes = [];
        }

        if (action.type !== undefined && this.state.type_configs[action.type] !== undefined) {
            let config = this.state.type_configs[action.type];
            if (action.label === undefined) {
                action.label = config.label;
            }
            action.classes = config.classes.concat(action.classes);
            if (action.handler === undefined) {
                action.handler = config.handler;
            }
        }
        if (action.label === undefined) {
            throw new Error('Action label is required');
        }
        if (action.handler === undefined) {
            throw new Error('Action handler is required');
        }
        action.elem = $(action_tpl({
            idx: action.idx,
            label: action.label,
            classes: action.classes.length > 0 ? ` ${action.classes.join(' ')}` : ''
        }));
        this.state.actions.set(action.idx, action);
        // insert new action at requested position, otherwise just append to actions
        if (lang.isNumber(config.position_before)) {
            let anchor = this.state.actions.get(config.position_before);
            if (anchor !== undefined) {
                anchor.elem.before(action.elem);
            }
        } else if (lang.isNumber(config.position_after)) {
            let anchor = this.state.actions.get(config.position_before);
            if (anchor !== undefined) {
                anchor.elem.after(action.elem);
            }
        } else {
            this.elem.actions.append(action.elem);
        }
        return action.idx;
    };

    /**
     * Remove action by index
     *
     * @param {number} idx
     */
    removeAction(idx) {
        let action = this.state.actions.get(idx);
        if (action === undefined) {
            return;
        }
        action.elem.remove();
        this.state.actions.delete(idx);
    };

    /**
     * Determines if modal is in a working state
     *
     * @returns {boolean}
     */
    isWorking() {
        return this.state.working;
    };

    /**
     * Start working state
     */
    startWorking() {
        if (this.elem.working === null) {
            return;
        }
        for (let action of this.state.actions.values()) {
            action.elem.prop('disabled', true);
        }
        this.elem.working.addClass('t-show');
        this.state.working = true;
    };

    /**
     * Reset working state
     */
    resetWorking() {
        if (this.elem.working === null || !this.state.working) {
            return;
        }
        for (let action of this.state.actions.values()) {
            action.elem.prop('disabled', false);
        }
        this.elem.working.removeClass('t-show');
        this.state.working = false;
    };

    /**
     * Open modal
     *
     * @returns {module:Modal.Base}
     */
    open() {
        this.state.reveal.open();
        return this;
    };

    /**
     * Close modal
     *
     * @returns {module:Modal.Base}
     */
    close() {
        this.clearMessages();
        this.elem.root.scrollTop(0);
        this.state.reveal.close();
        return this;
    };

    /**
     * Destroy modal
     *
     * @returns {module:Modal.Base}
     */
    destroy() {
        this.state.reveal.destroy();
        return this;
    };
}

module.exports = Base;
