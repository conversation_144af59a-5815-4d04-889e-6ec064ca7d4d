'use strict';

import {Base} from '@ca-submodule/modal';
import company_info_modal_tpl from '@cam-customer-portal-tpl/modals/company-info.hbs';

/**
 * Company Information Modal
 * 
 * Displays company information in a modal dialog
 */
export class CompanyInfoModal extends Base {
    /**
     * Constructor
     */
    constructor() {
        super('', {
            size: Base.Size.SMALL,
            closable: true,
            wrapper: true,
            classes: ['c-company-info-modal']
        });
        
        this.setTitle('Company Information');
        
        this.addAction({
            type: Base.Action.CLOSE
        });
    }

    /**
     * Open modal with company data
     * 
     * @param {Object} data - Company and salesperson data
     * @param {Object} data.company - Company information
     * @param {Object} data.salesperson - Salesperson information
     */
    open(data) {
        this.setContent(company_info_modal_tpl({
            company: data.company || {},
            salesperson: data.salesperson || null
        }));
        super.open();
    }
}
