'use strict';

import Page from '@ca-package/router/src/page';
import gallery_tpl from '@cam-customer-portal-tpl/pages/main-pages/gallery.hbs';
import { Client } from '../../lib/client';
import { lightboxGallery } from '../../lib/lightbox-gallery';
import { createErrorMessage } from '@cas-notification-toast-js/message/error';

/**
 * Gallery page for customer portal
 *
 * @memberof module:CustomerPortal/Pages/MainPages
 */
export class GalleryPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            customer_data: window.customer_portal_data || {},
            images: [],
            loading: true,
            loaded: false,
            error: null
        });

        this.client = new Client(this.state.customer_data?.customerUUID);
    }

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    async boot(root) {
        super.boot(root);
        this.setupImageCardHandlers();
    }

    /**
     * Setup click handlers for image cards
     */
    setupImageCardHandlers() {
        if (this.elem && this.elem.root && this.state.uploads) {
            lightboxGallery.setupImageCardHandlers(
                this.elem.root,
                this.state.uploads,
                '.m-image-card',
                'data-file-id'
            );
        }
    }

    /**
     * Fetch data from an API endpoint
     *
     * @returns {Promise<void>}
     */
    async fetchData() {
        try {
            this.state.loading = true;
            this.state.error = null;

            const uploads = await this.client.fetchData('uploads');

            // Update state with fetched data
            Object.assign(this.state, {
                uploads
            });

            this.state.loaded = true;
            console.log('Data loaded successfully:', this.state.data);
        } catch (error) {
            console.error('Error fetching overview data:', error);
            this.state.error = error;
            const message = createErrorMessage('Unable to load overview data. Please refresh the page and try again.');
            this.router.main_route.layout.toasts.addMessage(message);
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (this.state.parent && typeof this.state.parent.showLoader === 'function') {
            this.state.parent.showLoader();
        }

        try {
            await this.fetchData();
            this.updateContent();
        } finally {
            if (this.state.parent && typeof this.state.parent.hideLoader === 'function') {
                this.state.parent.hideLoader();
            }
        }

        await super.load(request, next);
    }

    /**
     * Update the page content after data changes
     */
    updateContent() {
        if (this.elem && this.elem.root) {
            this.elem.root.html(this.render());
            this.setupImageCardHandlers();
        }
    }

    /**
     * Unload page and cleanup
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        if (this.elem && this.elem.root) {
            lightboxGallery.removeImageCardHandlers(this.elem.root, '.m-image-card');
        }

        await super.unload(request, next);
    }

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return gallery_tpl({
            customer_data: this.state.customer_data,
            uploads: this.state.uploads
        });
    }
}