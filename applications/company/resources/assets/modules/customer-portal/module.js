'use strict';

import './resources/sass/main.scss';

const $ = require('jquery');
window.$ = window.jQuery = $;

require('@fancyapps/fancybox');

import {find, jsSelector} from '@ca-package/dom';
require('@ca-package/dom/src/jquery_plugin');

require('remixicon/icons/System/dashboard-line.svg');
require('remixicon/icons/Business/calendar-line.svg');
require('remixicon/icons/Document/file-list-2-line.svg');
require('remixicon/icons/Document/receipt-line.svg');
require('remixicon/icons/Media/image-line.svg');
require('remixicon/icons/User & Faces/user-settings-line.svg');
require('remixicon/icons/Map/direction-line.svg');
require('remixicon/icons/Device/phone-line.svg');
require('remixicon/icons/Business/mail-add-line.svg');
require('remixicon/icons/System/share-circle-line.svg');
require('remixicon/icons/System/time-line.svg');
require('remixicon/icons/User & Faces/account-circle-line.svg');
require('remixicon/icons/System/download-line.svg');
require('remixicon/icons/Document/file-line.svg');
require('remixicon/icons/System/eye-line.svg');
require('remixicon/icons/System/information-line.svg');
require('remixicon/icons/System/close-line.svg');

const svgs = require.context('./resources/svg-symbols', true, /\.svg$/);
svgs.keys().forEach(svgs);

import {Controller} from './src/index';
window.CustomerPortal = new Controller(find(jsSelector('customer-portal-root')));
