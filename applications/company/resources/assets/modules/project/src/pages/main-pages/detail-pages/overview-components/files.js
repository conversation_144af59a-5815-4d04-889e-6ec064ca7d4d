'use strict';

const Uppy = require('@uppy/core');
const Dashboard = require('@uppy/dashboard');
const XHRUpload = require('@uppy/xhr-upload');
const Webcam = require('@uppy/webcam');
const filesize = require('filesize');
const moment = require('moment-timezone');

const Api = require('@ca-package/api');
const Component = require('@ca-package/router/src/component');

const Table = require('@ca-submodule/table').Base;

const MediaIcon = require('@cac-js/utils/media_icon');

const files_tpl = require('@cam-project-tpl/pages/main-pages/detail-pages/overview-components/files.hbs');

class Files extends Component {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page} parent
     */
    constructor(router, name, parent) {
        super(router, name, parent);
        Object.assign(this.state, {
            table: null,
            no_files: false,
            gallery_extensions: ['jpg', 'jpeg', 'png', 'gif']
        });
    };

    /**
     * Get and cache edit modal
     *
     * @returns {object}
     */
    get edit_modal() {
        if (this.state.edit_modal === undefined) {
            let modal = require('@cam-project-js/modals/main/project/overview/files/edit');
            this.state.edit_modal = new modal();
        }
        return this.state.edit_modal;
    };

    /**
     * Open edit modal and return promise
     *
     * @param file_id
     * @returns {Promise<object|null>}
     */
    openEditModal(file_id) {
        return new Promise((resolve, reject) => {
            return this.edit_modal.open({
                file_id,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Get or set delete modal
     *
     * @returns {object}
     */
    get delete_modal() {
        if (this.state.delete_modal === undefined) {
            let modal = require('@cam-project-js/modals/main/project/overview/files/delete');
            this.state.delete_modal = new modal();
        }
        return this.state.delete_modal;
    };

    /**
     * Open delete modal and return promise
     *
     * @param file_id
     * @returns {Promise<string|null>}
     */
    openDeleteModal(file_id) {
        return new Promise((resolve, reject) => {
            return this.delete_modal.open({
                file_id,
                promise: {resolve, reject}
            });
        });
    };

    /**
     * Toggle if no files display is shown
     *
     * If table is in use, then it will be destroyed.
     *
     * @param {boolean} [active=true]
     */
    toggleNoFiles(active = true) {
        if (this.state.no_files === active) {
            return;
        }
        if (active) {
            this.removeTable();
        }
        this.state.no_files = active;
        this.elem.no_files.toggle(active);
    };

    /**
     * Get table instance
     *
     * @returns {Promise<module:Table.Base>}
     */
    async getTable() {
        if (this.state.table === null) {
            if (this.state.no_files) {
                this.toggleNoFiles(false);
            }
            this.elem.table.show();
            let table = new Table(this.elem.table, {
                page_lengths: [5, 10, 25],
                paging_type: Table.Paging.SIMPLE
            });
            table.setToolbar({
                filter: false,
                settings: false
            });
            table.setColumns({
                name: {
                    label: 'Name',
                    responsive: 1,
                    width: '85%',
                    value: (data, type) => {
                        if (type === 'display') {
                            let name = table.trimColumn(data.name, 50, true, true);
                            return `<svg class="c-fct--icon"><use xlink:href="#${MediaIcon.getIconFromContentType(data.file.content_type)}"></use></svg>${name}`;
                        }
                        return data.name;
                    }
                },
                created_at: {
                    label: 'Uploaded',
                    width: '10%',
                    value: (data, type) => {
                        let date = data.created_at;
                        if (type === 'display') {
                            return moment(date).tz(this.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                        }
                        return date;
                    }
                },
                size: {
                    label: 'Size',
                    width: '10%',
                    align: Table.Align.RIGHT,
                    value: (data, type) => type === 'display' || type === 'filter' ? filesize(data.file.size) : data.file.size
                }
            });
            table.setRowActions({
                view_gallery: {
                    label: 'View Gallery',
                    visible: data => this.state.gallery_extensions.indexOf(data.file.extension) !== -1,
                    action: data => this.openGallery(data.id)
                },
                edit: {
                    label: 'Edit',
                    action: data => {
                        this.openEditModal(data.id).then(result => {
                            if (result === null) {
                                return;
                            }
                            this.updateFile(result);
                        });
                    }
                },
                delete: {
                    label: 'Delete',
                    action: data => {
                        this.openDeleteModal(data.id).then(result => {
                            if (result === null) {
                                return;
                            }
                            this.removeFile(data.id);
                        });
                    }
                },
                download: {
                    label: 'Download',
                    link: {
                        href: data => `${data.file_media_urls.original}?download=true`,
                        target: '_blank'
                    }
                }
            });
            table.setState({
                pagination: {
                    per_page: 5
                },
                sorts: {
                    name: Table.Sort.ASC
                }
            });
            table.on('row_click', data => {
                if (this.state.gallery_extensions.indexOf(data.file.extension) !== -1) {
                    this.openGallery(data.id);
                    return;
                }
                window.open(data.file_media_urls.original, '_blank');
            });
            table.setData([]);
            this.state.table = table.setup();
        }
        return await this.state.table;
    };

    /**
     * Remove table if available
     */
    removeTable() {
        if (this.state.table === null) {
            return;
        }
        this.getTable().then(table => {
            table.destroy();
            this.state.table = null;
        });
        this.elem.table.hide();
    };

    /**
     * Add new file to table
     *
     * @param {object} file
     */
    addFile(file) {
        this.getTable().then(table => table.addRow(file));
    };

    /**
     * Update file in table
     *
     * @param {object} file
     */
    updateFile(file) {
        this.getTable().then(table => table.updateRow(file));
    };

    /**
     * Remove file from table
     *
     * @param {string} file_id - UUID
     */
    removeFile(file_id) {
        this.getTable().then(table => {
            table.deleteRow(file_id);
            if (table.getRows().length === 0) {
                this.toggleNoFiles(true);
            }
        });
    };

    /**
     * Open gallery of all images in table
     *
     * @param {string} file_id - UUID of file to start gallery on
     */
    openGallery(file_id) {
        this.getTable().then(table => {
            let rows = table.getRows(),
                images = [],
                file_index = null,
                index = 0;
            for (let i = 0; i < rows.length; i++) {
                let row = rows[i];
                if (this.state.gallery_extensions.indexOf(row.file.extension) === -1) {
                    continue;
                }
                images.push({
                    src: row.file_media_urls.original,
                    opts: {
                        caption: row.name,
                        thumb: row.file_media_urls.thumbnail
                    }
                });
                if (row.id === file_id) {
                    file_index = index;
                }
                index++;
            }
            $.fancybox.open(images, {
                buttons: ['thumbs', 'download', 'close'],
                thumbs: {
                    autoStart: true
                }
            }, file_index);
        });
    };

    /**
     * Load component
     *
     * @param {object} request
     */
    async load(request) {
        this.state.id = request.params.project_id;
        await super.load(request);

        let {entities: files} = await Api.Resources.ProjectFiles()
            .filters({project_id: request.params.project_id})
            .accept('application/vnd.adg.fx.collection-v1+json')
            .all();
        if (files.length === 0) {
            if (!window.project_data.features.files) {
                this.elem.root.hide();
                return;
            }
            this.toggleNoFiles(true);
            return;
        }
        files = files.map(entity => entity.data);
        (await this.getTable()).setTableData(files);
    };

    /**
     * Unload component
     *
     * @param {object} request
     */
    async unload(request) {
        this.toggleNoFiles(true);
        await super.unload(request);
    };

    /**
     * Enable upload functionality
     */
    enableUpload() {
        const max_files = 25;
        this.state.uppy = Uppy({
            id: 'project-file-upload',
            autoProceed: false,
            restrictions: {
                allowedFileTypes: [
                    // microsoft
                    '.ppt', '.pptx', '.doc', '.docx', '.xls', '.xlsx', '.msg',

                    // apple
                    '.pages', '.key', '.numbers',

                    // open document
                    '.pps', '.ppsx',

                    // images
                    '.jpeg', '.jpg', '.png', '.gif', '.tiff', '.tif', '.heic',

                    // video
                    '.mp4', '.m4v', '.mov', '.wmv', '.avi', '.ogv', '.webm', '.3gp',

                    // audio
                    '.mpga', '.oga', '.wav', '.mp3', '.ogg',

                    // document
                    '.pdf', '.csv', '.txt', '.dwg', '.rtf', '.html', '.htm', '.zip', '.eml'
                ],
                maxNumberOfFiles: max_files,
                maxFileSize: 62914560 // 60mb (changing this to 60mb but still displaying 50mb in the rules becuase of
                // what appears to be additional bloat iphone is giving the files when users try to upload from the gallery)
            }
        })
            .use(Dashboard, {
                metaFields: [
                    {id: 'name', name: 'Name', placeholder: 'File name'}
                ],
                closeModalOnClickOutside: true,
                hideProgressAfterFinish: true,
                proudlyDisplayPoweredByUppy: false,
                locale: {
                    strings: {
                        done: 'Back',
                    }
                },
                note: `1-${max_files} files allowed per upload, up to 50MB each`,
                onRequestCloseModal: () => {
                    this.state.dashboard.closeModal();
                    let files = Object.assign({}, this.state.uppy.getState().files);
                    if (Object.keys(files).length === 0) {
                        this.state.uppy.reset();
                    }
                }
            })
            .use(XHRUpload, {
                endpoint: Api.Resources.ProjectFiles().buildUrl(),
                method: 'POST',
                headers: {
                    Accept: 'application/vnd.adg.fx.collection-v1+json'
                },
                fieldName: 'file',
                metaFields: ['entity', 'name'],
                getResponseError: (responseText, response) => {
                    if (response.status === 401) {
                        window.location.href = window.fx_pages.AUTH_LOGIN;
                        return;
                    }
                    let error = JSON.parse(responseText);
                    if (response.status === 422) {
                        let text = [];
                        for (let key of Object.keys(error.errors)) {
                            text.push(error.errors[key]);
                        }
                        return new Error(text.join(', '));
                    }
                    return new Error('Upload failed for unknown reason, please contact support');
                }
            })
            .use(Webcam, {
                target: Dashboard,
                modes: ['picture'],
                facingMode: 'environment',
                mirror: false
            });

        this.state.dashboard = this.state.uppy.getPlugin('Dashboard');

        this.state.uppy
            .on('file-added', (file) => {
                this.state.uppy.setFileMeta(file.id, {
                    entity: JSON.stringify({project_id: parseInt(this.state.id)})
                });
            })
            .on('upload-success', (file, resp) => {
                this.addFile(resp.body);
            })
            .on('complete', () => {
                setTimeout(() => {
                    let close_modal = true;
                    let files = this.state.uppy.getState().files;
                    for (let id in files) {
                        let file = files[id];
                        let is_processing = file.progress.preprocess || file.progress.postprocess;
                        if (file.progress.uploadComplete && !is_processing && !file.error) {
                            this.state.uppy.removeFile(file.id);
                            continue;
                        }
                        close_modal = false;
                    }
                    if (this.state.dashboard.isModalOpen() && close_modal) {
                        this.state.dashboard.closeModal();
                    }
                }, 1000);
            });

        this.elem.add_file.show().fxClick(() => this.state.dashboard.openModal(), true);
    };

    /**
     * Boot component
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.add_file = this.elem.root.fxFind('upload-file').hide();
        this.elem.no_files = this.elem.root.fxFind('no-files').hide();
        this.elem.table = this.elem.root.fxFind('files-table').hide();

        if (window.project_data.features.files) {
            this.enableUpload();
        }
    };

    /**
     * Render component
     */
    render() {
        return files_tpl();
    };
}

module.exports = Files;
