<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.6/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         verbose="true"
         stopOnFailure="false"
         processIsolation="false"
         backupGlobals="false"
         backupStaticAttributes="false"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true">

    <testsuites>
        <testsuite name="Unit Tests">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="E2E Payment Tests">
            <directory>tests/e2e/payments</directory>
        </testsuite>
    </testsuites>

    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="QUEUE_DRIVER" value="sync"/>
        <!-- Local testing URL for payment form submissions -->
        <env name="PAYMENT_TEST_URL" value="http://app.contractoraccelerator.test/payments/Q1XPWMdQxtWGZY6lw4rpfwTTv9NXQv9Z?invoice_type=1&amp;bid_id=1"/>
        <!-- Staging URL for form analysis (read-only) -->
        <env name="PAYMENT_STAGING_URL" value="https://staging.app.contractoraccelerator.com/payments/qSwN61wh2mXSGmMd3pphgw8r2g68rlKD?invoice_type=1&amp;bid_id=1768"/>
        <!-- Test data file path -->
        <env name="PAYMENT_TEST_DATA_CSV" value="tests/e2e/payments/static/credit_cards.csv"/>
        <!-- Browser automation settings -->
        <env name="SELENIUM_BROWSER" value="chrome"/>
        <env name="SELENIUM_HEADLESS" value="true"/>
        <env name="SELENIUM_TIMEOUT" value="30"/>
        <!-- Test results output directory -->
        <env name="TEST_RESULTS_DIR" value="tests/e2e/payments/results"/>
    </php>

    <logging>
        <log type="coverage-html" target="tests/coverage"/>
        <log type="coverage-text" target="php://stdout" showUncoveredFiles="false"/>
        <log type="junit" target="tests/e2e/payments/results/junit.xml"/>
    </logging>

    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">app</directory>
            <exclude>
                <directory>vendor</directory>
                <directory>tests</directory>
                <directory>storage</directory>
                <directory>resources</directory>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
